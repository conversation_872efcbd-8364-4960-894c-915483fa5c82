import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/llm/openai_client.dart';
import 'package:bervin/llm/deepseek_client.dart';
import 'package:bervin/llm/claude_client.dart';
import 'package:bervin/llm/gemini_client.dart';
import 'package:bervin/llm/foundry_client.dart';
import 'package:bervin/llm/llm_factory.dart';

void main() {
  group('API Key Validation Tests', () {
    test('OpenAI client should throw error with empty API key', () {
      expect(
        () => OpenAIClient(apiKey: ''),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('OpenAI client should throw error with whitespace-only API key', () {
      expect(
        () => OpenAIClient(apiKey: '   '),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('DeepSeek client should throw error with empty API key', () {
      expect(
        () => DeepSeekClient(apiKey: ''),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('Claude client should throw error with empty API key', () {
      expect(
        () => ClaudeClient(apiKey: ''),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('Gemini client should throw error with empty API key', () {
      expect(
        () => GeminiClient(apiKey: ''),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('Foundry client should throw error with empty API key', () {
      expect(
        () => FoundryClient(apiKey: ''),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('OpenAI client should work with valid API key', () {
      expect(
        () => OpenAIClient(apiKey: 'sk-test123'),
        returnsNormally,
      );
    });

    test('LLM Factory should throw error for empty API key', () {
      expect(
        () => LLMFactory.create(
          LLMProvider.openai,
          apiKey: '',
          baseUrl: 'https://api.openai.com/v1',
        ),
        throwsA(isA<ArgumentError>()),
      );
    });
  });
}
