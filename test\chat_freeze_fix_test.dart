import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/llm/llm_factory.dart';
import 'package:bervin/llm/model.dart' as llm_model;
import 'package:bervin/provider/settings_provider.dart';

void main() {
  group('Chat Freeze Fix Tests', () {
    test('LLM Factory should handle empty API key gracefully', () {
      // Create a model with empty API key scenario
      final model = llm_model.Model(
        name: "gpt-4o-mini",
        label: "GPT-4o-mini", 
        providerId: "openai",
        icon: "openai",
        providerName: "OpenAI",
        apiStyle: "openai",
      );

      // This should throw an exception instead of creating a client with empty key
      expect(
        () => LLMFactoryHelper.createFromModel(model),
        throwsA(isA<Exception>()),
      );
    });

    test('LLM Factory should handle missing provider gracefully', () {
      final model = llm_model.Model(
        name: "test-model",
        label: "Test Model",
        providerId: "nonexistent",
        icon: "test",
        providerName: "Test Provider",
        apiStyle: "openai",
      );

      // This should throw an exception for missing provider
      expect(
        () => LLMFactoryHelper.createFromModel(model),
        throwsA(isA<Exception>()),
      );
    });

    test('Exception messages should be user-friendly', () {
      final model = llm_model.Model(
        name: "gpt-4o-mini",
        label: "GPT-4o-mini",
        providerId: "openai", 
        icon: "openai",
        providerName: "OpenAI",
        apiStyle: "openai",
      );

      try {
        LLMFactoryHelper.createFromModel(model);
        fail('Expected exception was not thrown');
      } catch (e) {
        // Verify the error message is user-friendly
        expect(e.toString(), contains('API key'));
        expect(e.toString(), contains('Settings'));
        expect(e.toString(), contains('API Configuration'));
      }
    });

    test('Ollama provider should fall back to OpenAI when not configured', () {
      final model = llm_model.Model(
        name: "llama2",
        label: "Llama 2",
        providerId: "ollama",
        icon: "ollama",
        providerName: "Ollama",
        apiStyle: "ollama",
      );

      // When Ollama is not properly configured, it should fall back to OpenAI
      // and require an API key, which should throw an exception
      expect(
        () => LLMFactoryHelper.createFromModel(model),
        throwsA(isA<Exception>()),
      );
    });
  });
}
