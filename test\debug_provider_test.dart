import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/provider/settings_provider.dart';
import 'package:bervin/llm/llm_factory.dart';
import 'package:bervin/llm/model.dart' as llm_model;

void main() {
  group('Debug Provider Tests', () {
    test('Should debug provider validation with single OpenAI provider', () {
      final settings = [
        LLMProviderSetting(
          providerId: 'openai',
          providerName: 'OpenAI',
          apiKey: 'sk-test123456789',
          apiEndpoint: 'https://api.openai.com/v1',
          enable: true,
        ),
        LLMProviderSetting(
          providerId: 'claude',
          providerName: 'Claude',
          apiKey: '', // Empty API key
          apiEndpoint: 'https://api.anthropic.com/v1',
          enable: true,
        ),
        LLMProviderSetting(
          providerId: 'ollama',
          providerName: 'Ollama',
          apiKey: '', // Ollama doesn't need API key
          apiEndpoint: 'http://localhost:11434',
          enable: true,
        ),
      ];

      // Test validation
      final result = _validateSingleProvider(settings);
      print('Validation result: isValid=${result.isValid}');
      print('Configured providers: ${result.configuredProviders}');
      print('Error message: ${result.errorMessage}');

      expect(result.isValid, true);
      expect(result.configuredProviders.length, 1);
      expect(result.configuredProviders.first, 'OpenAI');

      // Test getSingleConfiguredProvider
      final configuredProvider = _getSingleConfiguredProvider(settings);
      print('Single configured provider: ${configuredProvider?.providerId}');
      expect(configuredProvider, isNotNull);
      expect(configuredProvider!.providerId, 'openai');
    });

    test('Should debug what happens with multiple providers', () {
      final settings = [
        LLMProviderSetting(
          providerId: 'openai',
          providerName: 'OpenAI',
          apiKey: 'sk-test123456789',
          apiEndpoint: 'https://api.openai.com/v1',
          enable: true,
        ),
        LLMProviderSetting(
          providerId: 'claude',
          providerName: 'Claude',
          apiKey: 'sk-ant-test456',
          apiEndpoint: 'https://api.anthropic.com/v1',
          enable: true,
        ),
      ];

      final result = _validateSingleProvider(settings);
      print('Multiple providers validation: isValid=${result.isValid}');
      print('Error: ${result.errorMessage}');

      expect(result.isValid, false);
      expect(result.configuredProviders.length, 2);
    });

    test('Should debug empty API key scenarios', () {
      final settings = [
        LLMProviderSetting(
          providerId: 'openai',
          providerName: 'OpenAI',
          apiKey: '',
          apiEndpoint: 'https://api.openai.com/v1',
          enable: true,
        ),
        LLMProviderSetting(
          providerId: 'claude',
          providerName: 'Claude',
          apiKey: '   ', // Whitespace only
          apiEndpoint: 'https://api.anthropic.com/v1',
          enable: true,
        ),
      ];

      final result = _validateSingleProvider(settings);
      print('Empty keys validation: isValid=${result.isValid}');
      print('Configured providers: ${result.configuredProviders}');

      expect(result.isValid, true);
      expect(result.configuredProviders.length, 0);
    });
  });
}

// Helper functions to test the validation logic
ProviderValidationResult _validateSingleProvider(List<LLMProviderSetting> apiSettings) {
  final configuredProviders = <String>[];
  
  for (var setting in apiSettings) {
    // Skip Ollama as it doesn't require API keys
    if (setting.providerId == 'ollama') continue;
    
    // Check if provider has a non-empty API key and is enabled
    final isEnabled = setting.enable ?? true;
    if (isEnabled && setting.apiKey.isNotEmpty && setting.apiKey.trim().isNotEmpty) {
      configuredProviders.add(setting.providerName ?? setting.providerId ?? 'Unknown');
    }
  }
  
  if (configuredProviders.length > 1) {
    return ProviderValidationResult(
      isValid: false,
      errorMessage: 'Only one provider is allowed at a time. Currently configured: ${configuredProviders.join(", ")}. Please remove API keys from all but one provider.',
      configuredProviders: configuredProviders,
    );
  }
  
  return ProviderValidationResult(
    isValid: true,
    errorMessage: '',
    configuredProviders: configuredProviders,
  );
}

LLMProviderSetting? _getSingleConfiguredProvider(List<LLMProviderSetting> apiSettings) {
  final validationResult = _validateSingleProvider(apiSettings);
  if (!validationResult.isValid || validationResult.configuredProviders.isEmpty) {
    return null;
  }
  
  // Return the first (and should be only) configured provider
  for (var setting in apiSettings) {
    if (setting.providerId == 'ollama') continue;
    final isEnabled = setting.enable ?? true;
    if (isEnabled && setting.apiKey.isNotEmpty && setting.apiKey.trim().isNotEmpty) {
      return setting;
    }
  }
  
  return null;
}
