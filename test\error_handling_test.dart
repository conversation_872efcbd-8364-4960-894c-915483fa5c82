import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Error Handling Tests', () {
    test('API key error detection should work correctly', () {
      // Test various API key related error messages
      final apiKeyErrors = [
        'API key is required',
        'LLM client not initialized',
        'Client not initialized',
        'api key cannot be empty',
      ];

      for (final errorMsg in apiKeyErrors) {
        final isApiKeyError = _isApiKeyError(errorMsg);
        expect(isApiKeyError, true, reason: 'Should detect "$errorMsg" as API key error');
      }
    });

    test('Non-API key errors should not be detected as API key errors', () {
      final nonApiKeyErrors = [
        'Network connection failed',
        'Timeout occurred',
        'Permission denied',
        'File not found',
        'Invalid response',
      ];

      for (final errorMsg in nonApiKeyErrors) {
        final isApiKeyError = _isApiKeyError(errorMsg);
        expect(isApiKeyError, false, reason: 'Should not detect "$errorMsg" as API key error');
      }
    });

    test('User friendly error messages should be generated correctly', () {
      final testCases = {
        'API key is required': 'API key is missing or invalid',
        'LLM client not initialized': 'API key is missing or invalid',
        'connection failed': 'connection',
        'timeout occurred': 'timeout',
        'permission denied': 'permission',
        'random error': 'unknown', // Should fall back to unknown
      };

      for (final entry in testCases.entries) {
        final friendlyMessage = _getUserFriendlyErrorMessage(entry.key);
        expect(
          friendlyMessage.toLowerCase(),
          contains(entry.value.toLowerCase()),
          reason: 'Error "${entry.key}" should generate message containing "${entry.value}"',
        );
      }
    });
  });
}

// Helper functions to test the error detection logic
bool _isApiKeyError(String errorMessage) {
  final errorString = errorMessage.toLowerCase();
  return errorString.contains('api key') || 
         errorString.contains('llm client not initialized') ||
         errorString.contains('client not initialized');
}

String _getUserFriendlyErrorMessage(String error) {
  final errorString = error.toLowerCase();
  
  // Check for API key related errors first
  if (errorString.contains('api key') || 
      errorString.contains('llm client not initialized') ||
      errorString.contains('client not initialized')) {
    return 'API key is missing or invalid. Please configure your API key in Settings > Providers.';
  }
  
  final errorMap = {
    'connection': 'Network connection error',
    'timeout': 'Request timeout error',
    'permission': 'Permission error',
    'cancelled': 'User cancelled operation',
    'No element': 'No element found',
    'not found': 'Resource not found',
    'invalid': 'Invalid request',
    'unauthorized': 'Unauthorized access',
  };

  for (final entry in errorMap.entries) {
    if (errorString.contains(entry.key.toLowerCase())) {
      return entry.value;
    }
  }
  return 'Unknown error occurred';
}
