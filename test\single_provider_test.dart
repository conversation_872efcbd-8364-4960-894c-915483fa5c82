import 'package:flutter_test/flutter_test.dart';
import 'package:bervin/provider/settings_provider.dart';

void main() {
  group('Single Provider Validation Tests', () {
    test('Should allow single provider configuration', () {
      final settings = [
        LLMProviderSetting(
          providerId: 'openai',
          providerName: 'OpenAI',
          apiKey: 'sk-test123',
          apiEndpoint: 'https://api.openai.com/v1',
          enable: true,
        ),
        LLMProviderSetting(
          providerId: 'claude',
          providerName: 'Claude',
          apiKey: '', // Empty API key
          apiEndpoint: 'https://api.anthropic.com/v1',
          enable: true,
        ),
      ];

      final result = _validateSingleProvider(settings);
      expect(result.isValid, true);
      expect(result.configuredProviders.length, 1);
      expect(result.configuredProviders.first, 'OpenAI');
    });

    test('Should reject multiple provider configurations', () {
      final settings = [
        LLMProviderSetting(
          providerId: 'openai',
          providerName: 'OpenAI',
          apiKey: 'sk-test123',
          apiEndpoint: 'https://api.openai.com/v1',
          enable: true,
        ),
        LLMProviderSetting(
          providerId: 'claude',
          providerName: '<PERSON>',
          apiKey: 'sk-ant-test456',
          apiEndpoint: 'https://api.anthropic.com/v1',
          enable: true,
        ),
      ];

      final result = _validateSingleProvider(settings);
      expect(result.isValid, false);
      expect(result.configuredProviders.length, 2);
      expect(result.errorMessage, contains('Only one provider is allowed'));
      expect(result.errorMessage, contains('OpenAI'));
      expect(result.errorMessage, contains('Claude'));
    });

    test('Should allow Ollama alongside other providers', () {
      final settings = [
        LLMProviderSetting(
          providerId: 'openai',
          providerName: 'OpenAI',
          apiKey: 'sk-test123',
          apiEndpoint: 'https://api.openai.com/v1',
          enable: true,
        ),
        LLMProviderSetting(
          providerId: 'ollama',
          providerName: 'Ollama',
          apiKey: '', // Ollama doesn't need API key
          apiEndpoint: 'http://localhost:11434',
          enable: true,
        ),
      ];

      final result = _validateSingleProvider(settings);
      expect(result.isValid, true);
      expect(result.configuredProviders.length, 1);
      expect(result.configuredProviders.first, 'OpenAI');
    });

    test('Should ignore disabled providers', () {
      final settings = [
        LLMProviderSetting(
          providerId: 'openai',
          providerName: 'OpenAI',
          apiKey: 'sk-test123',
          apiEndpoint: 'https://api.openai.com/v1',
          enable: true,
        ),
        LLMProviderSetting(
          providerId: 'claude',
          providerName: 'Claude',
          apiKey: 'sk-ant-test456',
          apiEndpoint: 'https://api.anthropic.com/v1',
          enable: false, // Disabled
        ),
      ];

      final result = _validateSingleProvider(settings);
      expect(result.isValid, true);
      expect(result.configuredProviders.length, 1);
      expect(result.configuredProviders.first, 'OpenAI');
    });

    test('Should handle empty API keys correctly', () {
      final settings = [
        LLMProviderSetting(
          providerId: 'openai',
          providerName: 'OpenAI',
          apiKey: '   ', // Whitespace only
          apiEndpoint: 'https://api.openai.com/v1',
          enable: true,
        ),
        LLMProviderSetting(
          providerId: 'claude',
          providerName: 'Claude',
          apiKey: '',
          apiEndpoint: 'https://api.anthropic.com/v1',
          enable: true,
        ),
      ];

      final result = _validateSingleProvider(settings);
      expect(result.isValid, true);
      expect(result.configuredProviders.length, 0);
    });
  });
}

// Helper function to test the validation logic
ProviderValidationResult _validateSingleProvider(List<LLMProviderSetting> apiSettings) {
  final configuredProviders = <String>[];
  
  for (var setting in apiSettings) {
    // Skip Ollama as it doesn't require API keys
    if (setting.providerId == 'ollama') continue;
    
    // Check if provider has a non-empty API key and is enabled
    final isEnabled = setting.enable ?? true;
    if (isEnabled && setting.apiKey.isNotEmpty && setting.apiKey.trim().isNotEmpty) {
      configuredProviders.add(setting.providerName ?? setting.providerId ?? 'Unknown');
    }
  }
  
  if (configuredProviders.length > 1) {
    return ProviderValidationResult(
      isValid: false,
      errorMessage: 'Only one provider is allowed at a time. Currently configured: ${configuredProviders.join(", ")}. Please remove API keys from all but one provider.',
      configuredProviders: configuredProviders,
    );
  }
  
  return ProviderValidationResult(
    isValid: true,
    errorMessage: '',
    configuredProviders: configuredProviders,
  );
}
